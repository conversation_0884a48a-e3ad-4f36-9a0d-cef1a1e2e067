import { validationResult } from 'express-validator';
import { logger } from '../utils/logger.js';
export const validateRequest = (req, res, next) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        logger.warn('Validation failed:', {
            errors: errors.array(),
            body: req.body,
            params: req.params,
            query: req.query
        });
        return res.status(400).json({
            success: false,
            message: 'Validation failed',
            errors: errors.array(),
            code: 'VALIDATION_ERROR'
        });
    }
    next();
};
export const validateQuery = (req, res, next) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        logger.warn('Query validation failed:', {
            errors: errors.array(),
            query: req.query
        });
        return res.status(400).json({
            success: false,
            message: 'Query validation failed',
            errors: errors.array(),
            code: 'QUERY_VALIDATION_ERROR'
        });
    }
    next();
};
export const validateParams = (req, res, next) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        logger.warn('Params validation failed:', {
            errors: errors.array(),
            params: req.params
        });
        return res.status(400).json({
            success: false,
            message: 'Params validation failed',
            errors: errors.array(),
            code: 'PARAMS_VALIDATION_ERROR'
        });
    }
    next();
};
export default { validateRequest, validateQuery, validateParams };
