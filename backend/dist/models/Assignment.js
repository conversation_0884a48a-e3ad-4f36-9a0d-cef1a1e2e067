import mongoose from 'mongoose';
const assignmentSchema = new mongoose.Schema({
    title: {
        type: String,
        required: true,
        trim: true
    },
    description: {
        type: String,
        trim: true
    },
    course: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Course',
        required: true
    },
    lecturer: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
        required: true
    },
    dueDate: {
        type: Date,
        required: true
    },
    maxScore: {
        type: Number,
        required: true,
        min: 1
    },
    type: {
        type: String,
        enum: ['assignment', 'quiz', 'exam', 'project', 'participation'],
        required: true
    },
    instructions: {
        type: String
    },
    attachments: [{
            filename: String,
            originalName: String,
            mimetype: String,
            size: Number,
            url: String
        }],
    isPublished: {
        type: Boolean,
        default: true
    },
    submissions: [{
            student: {
                type: mongoose.Schema.Types.ObjectId,
                ref: 'User'
            },
            submittedAt: {
                type: Date,
                default: Date.now
            },
            files: [{
                    filename: String,
                    originalName: String,
                    mimetype: String,
                    size: Number,
                    url: String
                }],
            comments: String,
            grade: Number,
            feedback: String,
            gradedAt: Date,
            gradedBy: {
                type: mongoose.Schema.Types.ObjectId,
                ref: 'User'
            }
        }],
    lateSubmissionAllowed: {
        type: Boolean,
        default: true
    },
    latePenaltyPercentage: {
        type: Number,
        default: 10,
        min: 0,
        max: 100
    },
    allowResubmission: {
        type: Boolean,
        default: false
    },
    maxSubmissions: {
        type: Number,
        default: 1,
        min: 1
    },
    rubric: [{
            criterion: String,
            description: String,
            maxPoints: Number,
            weight: Number
        }],
    tags: [String],
    isGroupAssignment: {
        type: Boolean,
        default: false
    },
    maxGroupSize: {
        type: Number,
        default: 1,
        min: 1
    }
}, {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
});
// Indexes
assignmentSchema.index({ course: 1 });
assignmentSchema.index({ lecturer: 1 });
assignmentSchema.index({ dueDate: 1 });
assignmentSchema.index({ type: 1 });
assignmentSchema.index({ isPublished: 1 });
assignmentSchema.index({ 'submissions.student': 1 });
// Virtual for submission count
assignmentSchema.virtual('submissionCount').get(function () {
    return this.submissions.length;
});
// Virtual for grading status
assignmentSchema.virtual('gradingStatus').get(function () {
    const total = this.submissions.length;
    const graded = this.submissions.filter(sub => sub.grade !== undefined).length;
    if (total === 0)
        return 'No submissions';
    if (graded === total)
        return 'All graded';
    if (graded === 0)
        return 'Not graded';
    return `${graded}/${total} graded`;
});
// Virtual for time remaining
assignmentSchema.virtual('timeRemaining').get(function () {
    const now = new Date();
    const due = new Date(this.dueDate);
    const diff = due.getTime() - now.getTime();
    if (diff <= 0)
        return 'Overdue';
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));
    const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    if (days > 0)
        return `${days} days, ${hours} hours`;
    return `${hours} hours`;
});
// Pre-save middleware to validate dates
assignmentSchema.pre('save', function (next) {
    if (this.dueDate && this.dueDate < new Date()) {
        // Allow past due dates for editing existing assignments
        if (this.isNew) {
            return next(new Error('Due date cannot be in the past'));
        }
    }
    next();
});
// Method to submit assignment
assignmentSchema.methods.submitAssignment = function (studentId, files, comments) {
    // Check if already submitted
    const existingSubmission = this.submissions.find(sub => sub.student.toString() === studentId);
    if (existingSubmission && !this.allowResubmission) {
        throw new Error('Assignment already submitted');
    }
    // Check submission limit
    const studentSubmissions = this.submissions.filter(sub => sub.student.toString() === studentId);
    if (studentSubmissions.length >= this.maxSubmissions) {
        throw new Error('Maximum submissions reached');
    }
    // Check if past due date
    const now = new Date();
    const isLate = now > this.dueDate;
    const submission = {
        student: studentId,
        submittedAt: now,
        files: files || [],
        comments: comments || '',
        isLate: isLate
    };
    this.submissions.push(submission);
    return this.save();
};
// Method to grade submission
assignmentSchema.methods.gradeSubmission = function (studentId, grade, feedback, gradedBy) {
    const submission = this.submissions.find(sub => sub.student.toString() === studentId);
    if (!submission) {
        throw new Error('Submission not found');
    }
    submission.grade = grade;
    submission.feedback = feedback;
    submission.gradedAt = new Date();
    submission.gradedBy = gradedBy;
    return this.save();
};
// Method to get student submission
assignmentSchema.methods.getStudentSubmission = function (studentId) {
    return this.submissions.find(sub => sub.student.toString() === studentId);
};
// Static method to find by course
assignmentSchema.statics.findByCourse = function (courseId) {
    return this.find({ course: courseId }).populate('lecturer', 'name email');
};
// Static method to find by lecturer
assignmentSchema.statics.findByLecturer = function (lecturerId) {
    return this.find({ lecturer: lecturerId }).populate('course', 'code title');
};
// Static method to find upcoming assignments
assignmentSchema.statics.findUpcoming = function (days = 7) {
    const futureDate = new Date();
    futureDate.setDate(futureDate.getDate() + days);
    return this.find({
        dueDate: { $lte: futureDate, $gte: new Date() },
        isPublished: true
    }).populate('course', 'code title');
};
// Static method to find overdue assignments
assignmentSchema.statics.findOverdue = function () {
    return this.find({
        dueDate: { $lt: new Date() },
        isPublished: true
    }).populate('course', 'code title');
};
export const Assignment = mongoose.model('Assignment', assignmentSchema);
export default Assignment;
