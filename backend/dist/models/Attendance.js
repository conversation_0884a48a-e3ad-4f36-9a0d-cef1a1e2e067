import mongoose from 'mongoose';
const attendanceSchema = new mongoose.Schema({
    student: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
        required: true
    },
    session: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Session',
        required: true
    },
    date: {
        type: Date,
        required: true
    },
    status: {
        type: String,
        enum: ['present', 'absent', 'late', 'excused'],
        required: true
    },
    markedBy: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
        required: true
    },
    markedAt: {
        type: Date,
        default: Date.now
    },
    notes: {
        type: String
    },
    excuseReason: {
        type: String
    },
    excuseDocument: {
        type: String
    },
    isExcused: {
        type: Boolean,
        default: false
    }
}, {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
});
// Indexes
attendanceSchema.index({ student: 1, session: 1 }, { unique: true });
attendanceSchema.index({ student: 1 });
attendanceSchema.index({ session: 1 });
attendanceSchema.index({ date: 1 });
attendanceSchema.index({ status: 1 });
attendanceSchema.index({ markedBy: 1 });
// Virtual for attendance percentage
attendanceSchema.virtual('attendancePercentage').get(function () {
    const statusValues = {
        'present': 100,
        'late': 80,
        'excused': 100,
        'absent': 0
    };
    return statusValues[this.status] || 0;
});
// Method to excuse absence
attendanceSchema.methods.excuse = function (reason, document) {
    this.status = 'excused';
    this.excuseReason = reason;
    this.excuseDocument = document;
    this.isExcused = true;
    return this.save();
};
// Method to mark present
attendanceSchema.methods.markPresent = function () {
    this.status = 'present';
    this.isExcused = false;
    return this.save();
};
// Method to mark absent
attendanceSchema.methods.markAbsent = function () {
    this.status = 'absent';
    this.isExcused = false;
    return this.save();
};
// Method to mark late
attendanceSchema.methods.markLate = function () {
    this.status = 'late';
    this.isExcused = false;
    return this.save();
};
// Static method to find by student
attendanceSchema.statics.findByStudent = function (studentId) {
    return this.find({ student: studentId }).populate('session markedBy');
};
// Static method to find by session
attendanceSchema.statics.findBySession = function (sessionId) {
    return this.find({ session: sessionId }).populate('student markedBy');
};
// Static method to find by date range
attendanceSchema.statics.findByDateRange = function (startDate, endDate) {
    return this.find({
        date: { $gte: startDate, $lte: endDate }
    }).populate('student session markedBy');
};
// Static method to calculate attendance percentage
attendanceSchema.statics.calculateAttendancePercentage = function (studentId, courseId) {
    return this.aggregate([
        {
            $lookup: {
                from: 'sessions',
                localField: 'session',
                foreignField: '_id',
                as: 'sessionData'
            }
        },
        {
            $unwind: '$sessionData'
        },
        {
            $match: {
                student: mongoose.Types.ObjectId(studentId),
                'sessionData.course': mongoose.Types.ObjectId(courseId)
            }
        },
        {
            $group: {
                _id: null,
                total: { $sum: 1 },
                present: {
                    $sum: {
                        $cond: [
                            { $in: ['$status', ['present', 'late', 'excused']] },
                            1,
                            0
                        ]
                    }
                }
            }
        },
        {
            $project: {
                percentage: {
                    $cond: [
                        { $eq: ['$total', 0] },
                        0,
                        { $multiply: [{ $divide: ['$present', '$total'] }, 100] }
                    ]
                }
            }
        }
    ]);
};
export const Attendance = mongoose.model('Attendance', attendanceSchema);
export default Attendance;
