import mongoose from 'mongoose';
const departmentSchema = new mongoose.Schema({
    name: {
        type: String,
        required: true,
        unique: true,
        trim: true
    },
    code: {
        type: String,
        required: true,
        unique: true,
        trim: true,
        uppercase: true
    },
    description: {
        type: String,
        trim: true
    },
    head: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User'
    },
    budget: {
        type: Number,
        default: 0,
        min: 0
    },
    established: {
        type: Date,
        default: Date.now
    },
    location: {
        building: String,
        floor: String,
        room: String
    },
    contact: {
        phone: String,
        email: String,
        fax: String
    },
    website: {
        type: String
    },
    isActive: {
        type: Boolean,
        default: true
    },
    programs: [{
            name: String,
            code: String,
            level: String,
            duration: Number,
            credits: Number,
            description: String
        }],
    facilities: [{
            name: String,
            type: String,
            capacity: Number,
            description: String
        }],
    staff: [{
            user: {
                type: mongoose.Schema.Types.ObjectId,
                ref: 'User'
            },
            role: String,
            startDate: Date,
            endDate: Date
        }],
    goals: [String],
    achievements: [String],
    partnerships: [{
            name: String,
            type: String,
            description: String,
            startDate: Date,
            endDate: Date
        }]
}, {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
});
// Indexes
departmentSchema.index({ name: 1 });
departmentSchema.index({ code: 1 });
departmentSchema.index({ head: 1 });
departmentSchema.index({ isActive: 1 });
// Virtual for staff count
departmentSchema.virtual('staffCount').get(function () {
    return this.staff.filter(s => !s.endDate || s.endDate > new Date()).length;
});
// Virtual for program count
departmentSchema.virtual('programCount').get(function () {
    return this.programs.length;
});
// Virtual for facility count
departmentSchema.virtual('facilityCount').get(function () {
    return this.facilities.length;
});
// Virtual for budget utilization
departmentSchema.virtual('budgetUtilization').get(function () {
    // This would be calculated based on actual expenses
    return 0;
});
// Method to add staff member
departmentSchema.methods.addStaff = function (userId, role, startDate) {
    this.staff.push({
        user: userId,
        role: role,
        startDate: startDate || new Date()
    });
    return this.save();
};
// Method to remove staff member
departmentSchema.methods.removeStaff = function (userId, endDate) {
    const staffMember = this.staff.find(s => s.user.toString() === userId);
    if (staffMember) {
        staffMember.endDate = endDate || new Date();
    }
    return this.save();
};
// Method to add program
departmentSchema.methods.addProgram = function (programData) {
    this.programs.push(programData);
    return this.save();
};
// Method to remove program
departmentSchema.methods.removeProgram = function (programCode) {
    this.programs = this.programs.filter(p => p.code !== programCode);
    return this.save();
};
// Method to add facility
departmentSchema.methods.addFacility = function (facilityData) {
    this.facilities.push(facilityData);
    return this.save();
};
// Method to remove facility
departmentSchema.methods.removeFacility = function (facilityName) {
    this.facilities = this.facilities.filter(f => f.name !== facilityName);
    return this.save();
};
// Method to update budget
departmentSchema.methods.updateBudget = function (newBudget) {
    this.budget = newBudget;
    return this.save();
};
// Static method to find by head
departmentSchema.statics.findByHead = function (headId) {
    return this.find({ head: headId, isActive: true });
};
// Static method to find by program
departmentSchema.statics.findByProgram = function (programCode) {
    return this.find({ 'programs.code': programCode, isActive: true });
};
// Static method to find by facility
departmentSchema.statics.findByFacility = function (facilityType) {
    return this.find({ 'facilities.type': facilityType, isActive: true });
};
// Static method to get department statistics
departmentSchema.statics.getStatistics = function () {
    return this.aggregate([
        { $match: { isActive: true } },
        {
            $group: {
                _id: null,
                totalDepartments: { $sum: 1 },
                totalBudget: { $sum: '$budget' },
                averageBudget: { $avg: '$budget' },
                totalPrograms: { $sum: { $size: '$programs' } },
                totalFacilities: { $sum: { $size: '$facilities' } }
            }
        }
    ]);
};
export const Department = mongoose.model('Department', departmentSchema);
export default Department;
