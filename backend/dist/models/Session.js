import mongoose from 'mongoose';
const sessionSchema = new mongoose.Schema({
    course: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Course',
        required: true
    },
    lecturer: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
        required: true
    },
    date: {
        type: Date,
        required: true
    },
    startTime: {
        type: String,
        required: true
    },
    endTime: {
        type: String,
        required: true
    },
    room: {
        type: String,
        trim: true
    },
    type: {
        type: String,
        enum: ['lecture', 'lab', 'tutorial', 'seminar'],
        required: true
    },
    topic: {
        type: String,
        trim: true
    },
    description: {
        type: String,
        trim: true
    },
    objectives: [String],
    materials: [{
            title: String,
            type: String,
            url: String,
            description: String
        }],
    attendanceTaken: {
        type: Boolean,
        default: false
    },
    attendanceTakenAt: {
        type: Date
    },
    attendanceTakenBy: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User'
    },
    isActive: {
        type: Boolean,
        default: true
    },
    notes: {
        type: String
    },
    homework: {
        type: String
    },
    nextSession: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Session'
    },
    previousSession: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Session'
    }
}, {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
});
// Indexes
sessionSchema.index({ course: 1 });
sessionSchema.index({ lecturer: 1 });
sessionSchema.index({ date: 1 });
sessionSchema.index({ type: 1 });
sessionSchema.index({ isActive: 1 });
sessionSchema.index({ attendanceTaken: 1 });
// Virtual for duration
sessionSchema.virtual('duration').get(function () {
    const start = new Date(`2000-01-01T${this.startTime}`);
    const end = new Date(`2000-01-01T${this.endTime}`);
    const diff = end.getTime() - start.getTime();
    return Math.round(diff / (1000 * 60)); // Duration in minutes
});
// Virtual for status
sessionSchema.virtual('status').get(function () {
    const now = new Date();
    const sessionDate = new Date(this.date);
    const sessionStart = new Date(`${sessionDate.toISOString().split('T')[0]}T${this.startTime}`);
    const sessionEnd = new Date(`${sessionDate.toISOString().split('T')[0]}T${this.endTime}`);
    if (now < sessionStart)
        return 'upcoming';
    if (now >= sessionStart && now <= sessionEnd)
        return 'ongoing';
    if (now > sessionEnd)
        return 'completed';
    return 'unknown';
});
// Virtual for attendance count
sessionSchema.virtual('attendanceCount').get(function () {
    // This would be populated by aggregation
    return 0;
});
// Pre-save middleware to validate times
sessionSchema.pre('save', function (next) {
    if (this.startTime && this.endTime) {
        const start = new Date(`2000-01-01T${this.startTime}`);
        const end = new Date(`2000-01-01T${this.endTime}`);
        if (start >= end) {
            return next(new Error('Start time must be before end time'));
        }
    }
    next();
});
// Method to take attendance
sessionSchema.methods.takeAttendance = function (takenBy) {
    this.attendanceTaken = true;
    this.attendanceTakenAt = new Date();
    this.attendanceTakenBy = takenBy;
    return this.save();
};
// Method to cancel session
sessionSchema.methods.cancel = function () {
    this.isActive = false;
    return this.save();
};
// Method to reschedule session
sessionSchema.methods.reschedule = function (newDate, newStartTime, newEndTime) {
    this.date = newDate;
    this.startTime = newStartTime;
    this.endTime = newEndTime;
    this.attendanceTaken = false;
    this.attendanceTakenAt = undefined;
    this.attendanceTakenBy = undefined;
    return this.save();
};
// Static method to find by course
sessionSchema.statics.findByCourse = function (courseId) {
    return this.find({ course: courseId, isActive: true }).sort({ date: 1 });
};
// Static method to find by lecturer
sessionSchema.statics.findByLecturer = function (lecturerId) {
    return this.find({ lecturer: lecturerId, isActive: true }).sort({ date: 1 });
};
// Static method to find by date range
sessionSchema.statics.findByDateRange = function (startDate, endDate) {
    return this.find({
        date: { $gte: startDate, $lte: endDate },
        isActive: true
    }).populate('course lecturer').sort({ date: 1 });
};
// Static method to find upcoming sessions
sessionSchema.statics.findUpcoming = function (days = 7) {
    const futureDate = new Date();
    futureDate.setDate(futureDate.getDate() + days);
    return this.find({
        date: { $lte: futureDate, $gte: new Date() },
        isActive: true
    }).populate('course lecturer').sort({ date: 1 });
};
// Static method to find today's sessions
sessionSchema.statics.findTodaysSessions = function () {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);
    return this.find({
        date: { $gte: today, $lt: tomorrow },
        isActive: true
    }).populate('course lecturer').sort({ startTime: 1 });
};
export const Session = mongoose.model('Session', sessionSchema);
export default Session;
