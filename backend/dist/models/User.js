import mongoose from 'mongoose';
import bcrypt from 'bcrypt';
import argon2 from 'argon2';
import speakeasy from 'speakeasy';
const userSchema = new mongoose.Schema({
    email: {
        type: String,
        required: true,
        unique: true,
        lowercase: true,
        trim: true,
        index: true
    },
    password: {
        type: String,
        required: true,
        minlength: 8
    },
    name: {
        type: String,
        required: true,
        trim: true
    },
    roles: [{
            type: String,
            enum: ['student', 'lecturer', 'admin', 'finance', 'registrar'],
            required: true
        }],
    studentId: {
        type: String,
        unique: true,
        sparse: true,
        trim: true
    },
    staffId: {
        type: String,
        unique: true,
        sparse: true,
        trim: true
    },
    department: {
        type: String,
        trim: true
    },
    level: {
        type: String,
        trim: true
    },
    programme: {
        type: String,
        trim: true
    },
    isActive: {
        type: Boolean,
        default: true
    },
    isLocked: {
        type: Boolean,
        default: false
    },
    loginAttempts: {
        type: Number,
        default: 0
    },
    lockUntil: {
        type: Date
    },
    lastLogin: {
        type: Date
    },
    mfaEnabled: {
        type: Boolean,
        default: false
    },
    mfaSecret: {
        type: String
    },
    refreshTokens: [{
            token: String,
            createdAt: {
                type: Date,
                default: Date.now,
                expires: 604800 // 7 days
            }
        }],
    profilePicture: {
        type: String
    },
    phone: {
        type: String,
        trim: true
    },
    address: {
        street: String,
        city: String,
        state: String,
        zipCode: String,
        country: String
    },
    emergencyContact: {
        name: String,
        relationship: String,
        phone: String,
        email: String
    }
}, {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
});
// Indexes
userSchema.index({ email: 1 });
userSchema.index({ studentId: 1 });
userSchema.index({ staffId: 1 });
userSchema.index({ department: 1 });
userSchema.index({ roles: 1 });
// Virtual for full name
userSchema.virtual('fullName').get(function () {
    return this.name;
});
// Pre-save middleware to hash password
userSchema.pre('save', async function (next) {
    if (!this.isModified('password'))
        return next();
    try {
        // Use Argon2id for staff, bcrypt for students
        const isStaff = this.roles.some(role => ['lecturer', 'admin', 'finance', 'registrar'].includes(role));
        if (isStaff) {
            this.password = await argon2.hash(this.password, {
                type: argon2.argon2id,
                memoryCost: 2 ** 16, // 64 MB
                timeCost: 3,
                parallelism: 1
            });
        }
        else {
            const saltRounds = 12;
            this.password = await bcrypt.hash(this.password, saltRounds);
        }
        next();
    }
    catch (error) {
        next(error);
    }
});
// Method to compare password
userSchema.methods.comparePassword = async function (candidatePassword) {
    try {
        const isStaff = this.roles.some(role => ['lecturer', 'admin', 'finance', 'registrar'].includes(role));
        if (isStaff) {
            return await argon2.verify(this.password, candidatePassword);
        }
        else {
            return await bcrypt.compare(candidatePassword, this.password);
        }
    }
    catch (error) {
        throw error;
    }
};
// Method to increment login attempts
userSchema.methods.incLoginAttempts = function () {
    // If we have a previous lock that has expired, restart at 1
    if (this.lockUntil && this.lockUntil < Date.now()) {
        return this.updateOne({
            $unset: { lockUntil: 1 },
            $set: { loginAttempts: 1 }
        });
    }
    const updates = { $inc: { loginAttempts: 1 } };
    // Lock account after 5 failed attempts for 2 hours
    if (this.loginAttempts + 1 >= 5 && !this.isLocked) {
        updates.$set = { lockUntil: Date.now() + 2 * 60 * 60 * 1000 }; // 2 hours
    }
    return this.updateOne(updates);
};
// Method to reset login attempts
userSchema.methods.resetLoginAttempts = function () {
    return this.updateOne({
        $unset: { loginAttempts: 1, lockUntil: 1 }
    });
};
// Method to setup MFA
userSchema.methods.setupMFA = function () {
    const secret = speakeasy.generateSecret({
        name: `University Portal (${this.email})`,
        issuer: 'University Portal'
    });
    this.mfaSecret = secret.base32;
    this.mfaEnabled = true;
    return {
        secret: secret.base32,
        qrCodeUrl: secret.otpauth_url
    };
};
// Method to verify MFA token
userSchema.methods.verifyMFAToken = function (token) {
    return speakeasy.totp.verify({
        secret: this.mfaSecret,
        encoding: 'base32',
        token: token,
        window: 2 // Allow 2 time steps (60 seconds) tolerance
    });
};
// Method to disable MFA
userSchema.methods.disableMFA = function () {
    this.mfaEnabled = false;
    this.mfaSecret = undefined;
};
// Static method to find by email
userSchema.statics.findByEmail = function (email) {
    return this.findOne({ email: email.toLowerCase() });
};
// Static method to find active users
userSchema.statics.findActive = function () {
    return this.find({ isActive: true });
};
// Static method to find by role
userSchema.statics.findByRole = function (role) {
    return this.find({ roles: role, isActive: true });
};
// Static method to find by department
userSchema.statics.findByDepartment = function (department) {
    return this.find({ department: department, isActive: true });
};
export const User = mongoose.model('User', userSchema);
export default User;
