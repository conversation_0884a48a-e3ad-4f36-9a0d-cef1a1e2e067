import mongoose from 'mongoose';
import bcrypt from 'bcrypt';
import argon2 from 'argon2';
import { v4 as uuidv4 } from 'uuid';
const { Schema } = mongoose;
// User Schema
const userSchema = new Schema({
    _id: { type: String, default: uuidv4 },
    email: {
        type: String,
        required: true,
        unique: true,
        lowercase: true,
        index: true
    },
    password: { type: String, required: true },
    name: { type: String, required: true },
    roles: [{
            type: String,
            enum: ['student', 'lecturer', 'admin', 'finance', 'registrar'],
            required: true
        }],
    avatar: { type: String },
    studentId: { type: String, sparse: true },
    staffId: { type: String, sparse: true },
    department: { type: String },
    level: { type: Number },
    programme: { type: String },
    isActive: { type: Boolean, default: true },
    lastLogin: { type: Date },
    loginAttempts: { type: Number, default: 0 },
    lockUntil: { type: Date },
    mfaSecret: { type: String },
    mfaEnabled: { type: Boolean, default: false },
    refreshTokens: [{
            token: String,
            createdAt: { type: Date, default: Date.now, expires: '7d' }
        }],
    permissions: [{ type: String }],
    createdAt: { type: Date, default: Date.now },
    updatedAt: { type: Date, default: Date.now },
}, {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
});
// Compound indexes for performance
userSchema.index({ email: 1, isActive: 1 });
userSchema.index({ studentId: 1 }, { sparse: true });
userSchema.index({ staffId: 1 }, { sparse: true });
userSchema.index({ roles: 1, isActive: 1 });
// Password hashing middleware
userSchema.pre('save', async function (next) {
    if (!this.isModified('password'))
        return next();
    try {
        // Use Argon2id for staff, bcrypt for students
        if (this.roles.some(role => ['lecturer', 'admin', 'finance', 'registrar'].includes(role))) {
            this.password = await argon2.hash(this.password);
        }
        else {
            this.password = await bcrypt.hash(this.password, 12);
        }
        next();
    }
    catch (error) {
        next(error);
    }
});
// Password verification method
userSchema.methods.comparePassword = async function (candidatePassword) {
    try {
        // Check if password was hashed with Argon2id (staff) or bcrypt (students)
        if (this.password.startsWith('$argon2id')) {
            return await argon2.verify(this.password, candidatePassword);
        }
        else {
            return await bcrypt.compare(candidatePassword, this.password);
        }
    }
    catch (error) {
        throw new Error('Password comparison failed');
    }
};
// Account lockout methods
userSchema.methods.incLoginAttempts = function () {
    // If we have a previous lock that has expired, restart at 1
    if (this.lockUntil && this.lockUntil < Date.now()) {
        return this.updateOne({
            $unset: { lockUntil: 1 },
            $set: { loginAttempts: 1 }
        });
    }
    const updates = { $inc: { loginAttempts: 1 } };
    // Lock account after 5 failed attempts for 2 hours
    if (this.loginAttempts + 1 >= 5 && !this.isLocked) {
        updates.$set = { lockUntil: Date.now() + 2 * 60 * 60 * 1000 }; // 2 hours
    }
    return this.updateOne(updates);
};
userSchema.methods.resetLoginAttempts = function () {
    return this.updateOne({
        $unset: { loginAttempts: 1, lockUntil: 1 }
    });
};
userSchema.virtual('isLocked').get(function () {
    return !!(this.lockUntil && this.lockUntil > Date.now());
});
// Course Schema
const courseSchema = new Schema({
    _id: { type: String, default: uuidv4 },
    code: { type: String, required: true, unique: true },
    title: { type: String, required: true },
    description: { type: String },
    credits: { type: Number, required: true },
    lecturer: { type: String, ref: 'User', required: true },
    department: { type: String, required: true },
    schedule: [{
            day: { type: String, enum: ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'] },
            startTime: { type: String },
            endTime: { type: String },
            room: { type: String }
        }],
    enrolled: { type: Number, default: 0 },
    capacity: { type: Number, required: true },
    prerequisites: [{ type: String }],
    semester: { type: String, required: true },
    academicYear: { type: String, required: true },
    isActive: { type: Boolean, default: true },
    createdAt: { type: Date, default: Date.now },
    updatedAt: { type: Date, default: Date.now },
}, {
    timestamps: true
});
// Compound indexes for course queries
courseSchema.index({ code: 1, semester: 1 });
courseSchema.index({ lecturer: 1, semester: 1 });
courseSchema.index({ department: 1, semester: 1 });
courseSchema.index({ isActive: 1, semester: 1 });
// Enrollment Schema
const enrollmentSchema = new Schema({
    _id: { type: String, default: uuidv4 },
    student: { type: String, ref: 'User', required: true },
    course: { type: String, ref: 'Course', required: true },
    semester: { type: String, required: true },
    academicYear: { type: String, required: true },
    enrolledAt: { type: Date, default: Date.now },
    status: {
        type: String,
        enum: ['enrolled', 'dropped', 'completed', 'failed'],
        default: 'enrolled'
    },
    finalGrade: { type: String },
    creditsEarned: { type: Number },
}, {
    timestamps: true
});
// Compound indexes for enrollment queries
enrollmentSchema.index({ student: 1, semester: 1 });
enrollmentSchema.index({ course: 1, semester: 1 });
enrollmentSchema.index({ student: 1, course: 1 }, { unique: true });
// Grade Schema
const gradeSchema = new Schema({
    _id: { type: String, default: uuidv4 },
    student: { type: String, ref: 'User', required: true },
    course: { type: String, ref: 'Course', required: true },
    assignment: { type: String, ref: 'Assignment' },
    semester: { type: String, required: true },
    academicYear: { type: String, required: true },
    grade: { type: String },
    score: { type: Number },
    maxScore: { type: Number },
    percentage: { type: Number },
    type: {
        type: String,
        enum: ['assignment', 'quiz', 'exam', 'project', 'participation'],
        required: true
    },
    gradedBy: { type: String, ref: 'User' },
    gradedAt: { type: Date },
    comments: { type: String },
    isFinal: { type: Boolean, default: false },
}, {
    timestamps: true
});
// Compound indexes for grade queries
gradeSchema.index({ student: 1, course: 1, semester: 1 });
gradeSchema.index({ course: 1, semester: 1 });
gradeSchema.index({ student: 1, semester: 1 });
// Assignment Schema
const assignmentSchema = new Schema({
    _id: { type: String, default: uuidv4 },
    title: { type: String, required: true },
    description: { type: String },
    course: { type: String, ref: 'Course', required: true },
    lecturer: { type: String, ref: 'User', required: true },
    dueDate: { type: Date, required: true },
    maxScore: { type: Number, required: true },
    type: {
        type: String,
        enum: ['assignment', 'quiz', 'exam', 'project', 'participation'],
        required: true
    },
    instructions: { type: String },
    attachments: [{
            filename: String,
            url: String,
            size: Number,
            mimeType: String
        }],
    submissions: [{
            student: { type: String, ref: 'User' },
            submittedAt: { type: Date },
            files: [{
                    filename: String,
                    url: String,
                    size: Number,
                    mimeType: String
                }],
            grade: { type: Number },
            feedback: { type: String },
            gradedAt: { type: Date },
            gradedBy: { type: String, ref: 'User' }
        }],
    isPublished: { type: Boolean, default: false },
    createdAt: { type: Date, default: Date.now },
    updatedAt: { type: Date, default: Date.now },
}, {
    timestamps: true
});
// Compound indexes for assignment queries
assignmentSchema.index({ course: 1, dueDate: 1 });
assignmentSchema.index({ lecturer: 1, dueDate: 1 });
assignmentSchema.index({ isPublished: 1, dueDate: 1 });
// Attendance Schema
const attendanceSchema = new Schema({
    _id: { type: String, default: uuidv4 },
    student: { type: String, ref: 'User', required: true },
    course: { type: String, ref: 'Course', required: true },
    session: { type: String, ref: 'Session', required: true },
    date: { type: Date, required: true },
    status: {
        type: String,
        enum: ['present', 'absent', 'late', 'excused'],
        required: true
    },
    markedBy: { type: String, ref: 'User' },
    markedAt: { type: Date, default: Date.now },
    notes: { type: String },
}, {
    timestamps: true
});
// Compound indexes for attendance queries
attendanceSchema.index({ student: 1, course: 1, date: 1 });
attendanceSchema.index({ course: 1, date: 1 });
attendanceSchema.index({ student: 1, date: 1 });
// Session Schema
const sessionSchema = new Schema({
    _id: { type: String, default: uuidv4 },
    course: { type: String, ref: 'Course', required: true },
    lecturer: { type: String, ref: 'User', required: true },
    date: { type: Date, required: true },
    startTime: { type: String, required: true },
    endTime: { type: String, required: true },
    room: { type: String },
    type: {
        type: String,
        enum: ['lecture', 'lab', 'tutorial', 'seminar'],
        required: true
    },
    topic: { type: String },
    notes: { type: String },
    qrCode: { type: String },
    qrCodeExpiry: { type: Date },
    attendanceTaken: { type: Boolean, default: false },
}, {
    timestamps: true
});
// Compound indexes for session queries
sessionSchema.index({ course: 1, date: 1 });
sessionSchema.index({ lecturer: 1, date: 1 });
sessionSchema.index({ date: 1, startTime: 1 });
// Department Schema
const departmentSchema = new Schema({
    _id: { type: String, default: uuidv4 },
    name: { type: String, required: true, unique: true },
    code: { type: String, required: true, unique: true },
    head: { type: String, ref: 'User' },
    description: { type: String },
    budget: { type: Number, default: 0 },
    established: { type: Date },
    isActive: { type: Boolean, default: true },
    createdAt: { type: Date, default: Date.now },
    updatedAt: { type: Date, default: Date.now },
}, {
    timestamps: true
});
// Notification Schema
const notificationSchema = new Schema({
    _id: { type: String, default: uuidv4 },
    user: { type: String, ref: 'User', required: true },
    title: { type: String, required: true },
    message: { type: String, required: true },
    type: {
        type: String,
        enum: ['info', 'success', 'warning', 'error'],
        required: true
    },
    read: { type: Boolean, default: false },
    link: { type: String },
    metadata: { type: Schema.Types.Mixed },
    createdAt: { type: Date, default: Date.now },
}, {
    timestamps: true
});
// Compound indexes for notification queries
notificationSchema.index({ user: 1, read: 1, createdAt: -1 });
notificationSchema.index({ createdAt: 1 }, { expireAfterSeconds: 2592000 }); // 30 days
// Permission Schema
const permissionSchema = new Schema({
    _id: { type: String, default: uuidv4 },
    name: { type: String, required: true, unique: true },
    description: { type: String },
    resource: { type: String, required: true },
    action: { type: String, required: true },
    roles: [{ type: String }],
    isActive: { type: Boolean, default: true },
}, {
    timestamps: true
});
// Create models
const User = mongoose.model('User', userSchema);
const Course = mongoose.model('Course', courseSchema);
const Enrollment = mongoose.model('Enrollment', enrollmentSchema);
const Grade = mongoose.model('Grade', gradeSchema);
const Assignment = mongoose.model('Assignment', assignmentSchema);
const Attendance = mongoose.model('Attendance', attendanceSchema);
const Session = mongoose.model('Session', sessionSchema);
const Department = mongoose.model('Department', departmentSchema);
const Notification = mongoose.model('Notification', notificationSchema);
const Permission = mongoose.model('Permission', permissionSchema);
// Import payment models
import { PaymentMethod, PaymentTransaction, PaymentWebhook, PaymentReconciliation, Fee } from './Payment.js';
export { User, Course, Enrollment, Grade, Assignment, Attendance, Session, Department, Notification, Permission, PaymentMethod, PaymentTransaction, PaymentWebhook, PaymentReconciliation, Fee, };
