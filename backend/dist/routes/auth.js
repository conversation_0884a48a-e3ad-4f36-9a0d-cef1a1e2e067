import express from 'express';
import { body, validationResult } from 'express-validator';
import speakeasy from 'speakeasy';
import QRCode from 'qrcode';
import { v4 as uuidv4 } from 'uuid';
import { User } from '../models/index.js';
import { TokenManager, loginRateLimit } from '../middleware/auth.js';
import { logger, securityLogger, auditLogger } from '../utils/logger.js';
import { sendEmail } from '../utils/email.js';
const router = express.Router();
// Validation rules
const registerValidation = [
    body('email').isEmail().normalizeEmail(),
    body('password').isLength({ min: 8 }).withMessage('Password must be at least 8 characters'),
    body('name').trim().isLength({ min: 2 }).withMessage('Name must be at least 2 characters'),
    body('roles').isArray().withMessage('Roles must be an array'),
    body('roles.*').isIn(['student', 'lecturer', 'admin', 'finance', 'registrar']),
];
const loginValidation = [
    body('email').isEmail().normalizeEmail(),
    body('password').notEmpty().withMessage('Password is required'),
];
const mfaValidation = [
    body('token').isLength({ min: 6, max: 6 }).isNumeric(),
];
// Register new user
router.post('/register', loginRateLimit, registerValidation, async (req, res) => {
    try {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({
                success: false,
                message: 'Validation failed',
                errors: errors.array(),
                code: 'VALIDATION_ERROR'
            });
        }
        const { email, password, name, roles, studentId, staffId, department, level, programme } = req.body;
        // Check if user already exists
        const existingUser = await User.findOne({ email });
        if (existingUser) {
            return res.status(409).json({
                success: false,
                message: 'User already exists',
                code: 'USER_EXISTS'
            });
        }
        // Create new user
        const user = new User({
            email,
            password,
            name,
            roles,
            studentId,
            staffId,
            department,
            level,
            programme,
        });
        await user.save();
        // Generate tokens
        const tokenPayload = {
            userId: user._id,
            email: user.email,
            roles: user.roles,
            jti: uuidv4(),
        };
        const accessToken = TokenManager.generateAccessToken(tokenPayload);
        const refreshToken = TokenManager.generateRefreshToken(tokenPayload);
        // Store refresh token
        user.refreshTokens.push({ token: refreshToken });
        await user.save();
        // Log registration
        auditLogger('user_registered', 'user', user._id, {
            email: user.email,
            roles: user.roles,
            ip: req.ip,
        });
        // Send welcome email
        try {
            await sendEmail({
                to: user.email,
                subject: 'Welcome to University Portal',
                template: 'welcome',
                data: { name: user.name }
            });
        }
        catch (emailError) {
            logger.warn('Failed to send welcome email:', emailError);
        }
        res.status(201).json({
            success: true,
            message: 'User registered successfully',
            data: {
                user: {
                    id: user._id,
                    email: user.email,
                    name: user.name,
                    roles: user.roles,
                    studentId: user.studentId,
                    staffId: user.staffId,
                    department: user.department,
                    level: user.level,
                    programme: user.programme,
                },
                tokens: {
                    accessToken,
                    refreshToken,
                }
            }
        });
    }
    catch (error) {
        logger.error('Registration error:', error);
        res.status(500).json({
            success: false,
            message: 'Registration failed',
            code: 'REGISTRATION_ERROR'
        });
    }
});
// Login user
router.post('/login', loginRateLimit, loginValidation, async (req, res) => {
    try {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({
                success: false,
                message: 'Validation failed',
                errors: errors.array(),
                code: 'VALIDATION_ERROR'
            });
        }
        const { email, password } = req.body;
        // Find user
        const user = await User.findOne({ email, isActive: true });
        if (!user) {
            securityLogger('login_failed', {
                email,
                reason: 'user_not_found',
                ip: req.ip,
            });
            return res.status(401).json({
                success: false,
                message: 'Invalid credentials',
                code: 'INVALID_CREDENTIALS'
            });
        }
        // Check if account is locked
        if (user.isLocked) {
            securityLogger('login_failed', {
                email,
                reason: 'account_locked',
                ip: req.ip,
            });
            return res.status(423).json({
                success: false,
                message: 'Account is locked due to too many failed attempts',
                code: 'ACCOUNT_LOCKED'
            });
        }
        // Verify password
        const isValidPassword = await user.comparePassword(password);
        if (!isValidPassword) {
            await user.incLoginAttempts();
            securityLogger('login_failed', {
                email,
                reason: 'invalid_password',
                ip: req.ip,
            });
            return res.status(401).json({
                success: false,
                message: 'Invalid credentials',
                code: 'INVALID_CREDENTIALS'
            });
        }
        // Reset login attempts on successful login
        await user.resetLoginAttempts();
        // Update last login
        user.lastLogin = new Date();
        await user.save();
        // Generate tokens
        const tokenPayload = {
            userId: user._id,
            email: user.email,
            roles: user.roles,
            jti: uuidv4(),
            mfaVerified: false, // Will be updated after MFA verification if required
        };
        const accessToken = TokenManager.generateAccessToken(tokenPayload);
        const refreshToken = TokenManager.generateRefreshToken(tokenPayload);
        // Store refresh token
        user.refreshTokens.push({ token: refreshToken });
        await user.save();
        // Log successful login
        auditLogger('user_login', 'user', user._id, {
            email: user.email,
            ip: req.ip,
            userAgent: req.get('User-Agent'),
        });
        // Check if MFA is required
        const mfaRequiredRoles = ['lecturer', 'admin', 'finance', 'registrar'];
        const requiresMFA = user.roles.some(role => mfaRequiredRoles.includes(role));
        if (requiresMFA && user.mfaEnabled) {
            // Return temporary token that requires MFA verification
            return res.json({
                success: true,
                message: 'MFA verification required',
                data: {
                    user: {
                        id: user._id,
                        email: user.email,
                        name: user.name,
                        roles: user.roles,
                        mfaEnabled: user.mfaEnabled,
                    },
                    tokens: {
                        accessToken,
                        refreshToken,
                    },
                    requiresMFA: true,
                }
            });
        }
        res.json({
            success: true,
            message: 'Login successful',
            data: {
                user: {
                    id: user._id,
                    email: user.email,
                    name: user.name,
                    roles: user.roles,
                    studentId: user.studentId,
                    staffId: user.staffId,
                    department: user.department,
                    level: user.level,
                    programme: user.programme,
                    mfaEnabled: user.mfaEnabled,
                },
                tokens: {
                    accessToken,
                    refreshToken,
                },
                requiresMFA: false,
            }
        });
    }
    catch (error) {
        logger.error('Login error:', error);
        res.status(500).json({
            success: false,
            message: 'Login failed',
            code: 'LOGIN_ERROR'
        });
    }
});
// Refresh token
router.post('/refresh', async (req, res) => {
    try {
        const { refreshToken } = req.body;
        if (!refreshToken) {
            return res.status(400).json({
                success: false,
                message: 'Refresh token required',
                code: 'REFRESH_TOKEN_REQUIRED'
            });
        }
        // Verify refresh token
        const decoded = await TokenManager.verifyRefreshToken(refreshToken);
        // Find user
        const user = await User.findOne({
            _id: decoded.userId,
            'refreshTokens.token': refreshToken,
            isActive: true
        });
        if (!user) {
            return res.status(401).json({
                success: false,
                message: 'Invalid refresh token',
                code: 'INVALID_REFRESH_TOKEN'
            });
        }
        // Generate new tokens
        const tokenPayload = {
            userId: user._id,
            email: user.email,
            roles: user.roles,
            jti: uuidv4(),
        };
        const newAccessToken = TokenManager.generateAccessToken(tokenPayload);
        const newRefreshToken = TokenManager.generateRefreshToken(tokenPayload);
        // Remove old refresh token and add new one
        user.refreshTokens = user.refreshTokens.filter(rt => rt.token !== refreshToken);
        user.refreshTokens.push({ token: newRefreshToken });
        await user.save();
        res.json({
            success: true,
            message: 'Token refreshed successfully',
            data: {
                tokens: {
                    accessToken: newAccessToken,
                    refreshToken: newRefreshToken,
                }
            }
        });
    }
    catch (error) {
        logger.error('Token refresh error:', error);
        res.status(401).json({
            success: false,
            message: 'Invalid refresh token',
            code: 'INVALID_REFRESH_TOKEN'
        });
    }
});
// Logout
router.post('/logout', async (req, res) => {
    try {
        const { refreshToken } = req.body;
        const authHeader = req.headers.authorization;
        if (authHeader && authHeader.startsWith('Bearer ')) {
            const accessToken = authHeader.substring(7);
            const decoded = await TokenManager.verifyAccessToken(accessToken);
            // Blacklist the access token
            await TokenManager.blacklistToken(decoded.jti);
        }
        if (refreshToken) {
            // Remove refresh token from user
            await User.updateOne({ 'refreshTokens.token': refreshToken }, { $pull: { refreshTokens: { token: refreshToken } } });
        }
        res.json({
            success: true,
            message: 'Logout successful'
        });
    }
    catch (error) {
        logger.error('Logout error:', error);
        res.status(500).json({
            success: false,
            message: 'Logout failed',
            code: 'LOGOUT_ERROR'
        });
    }
});
// Setup MFA
router.post('/mfa/setup', async (req, res) => {
    try {
        const authHeader = req.headers.authorization;
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
            return res.status(401).json({
                success: false,
                message: 'Authentication required',
                code: 'AUTH_REQUIRED'
            });
        }
        const token = authHeader.substring(7);
        const decoded = await TokenManager.verifyAccessToken(token);
        const user = await User.findById(decoded.userId);
        if (!user) {
            return res.status(404).json({
                success: false,
                message: 'User not found',
                code: 'USER_NOT_FOUND'
            });
        }
        // Generate MFA secret
        const secret = speakeasy.generateSecret({
            name: `${user.email}`,
            issuer: 'University Portal',
            length: 32,
        });
        // Store secret temporarily (user needs to verify before enabling)
        user.mfaSecret = secret.base32;
        await user.save();
        // Generate QR code
        const qrCodeUrl = await QRCode.toDataURL(secret.otpauth_url);
        res.json({
            success: true,
            message: 'MFA setup initiated',
            data: {
                secret: secret.base32,
                qrCode: qrCodeUrl,
                manualEntryKey: secret.base32,
            }
        });
    }
    catch (error) {
        logger.error('MFA setup error:', error);
        res.status(500).json({
            success: false,
            message: 'MFA setup failed',
            code: 'MFA_SETUP_ERROR'
        });
    }
});
// Verify MFA setup
router.post('/mfa/verify', mfaValidation, async (req, res) => {
    try {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({
                success: false,
                message: 'Validation failed',
                errors: errors.array(),
                code: 'VALIDATION_ERROR'
            });
        }
        const authHeader = req.headers.authorization;
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
            return res.status(401).json({
                success: false,
                message: 'Authentication required',
                code: 'AUTH_REQUIRED'
            });
        }
        const token = authHeader.substring(7);
        const decoded = await TokenManager.verifyAccessToken(token);
        const user = await User.findById(decoded.userId);
        if (!user || !user.mfaSecret) {
            return res.status(404).json({
                success: false,
                message: 'MFA not set up',
                code: 'MFA_NOT_SETUP'
            });
        }
        const { token: mfaToken } = req.body;
        // Verify the MFA token
        const verified = speakeasy.totp.verify({
            secret: user.mfaSecret,
            encoding: 'base32',
            token: mfaToken,
            window: 2, // Allow 2 time steps tolerance
        });
        if (!verified) {
            return res.status(400).json({
                success: false,
                message: 'Invalid MFA token',
                code: 'INVALID_MFA_TOKEN'
            });
        }
        // Enable MFA
        user.mfaEnabled = true;
        await user.save();
        auditLogger('mfa_enabled', 'user', user._id, {
            email: user.email,
            ip: req.ip,
        });
        res.json({
            success: true,
            message: 'MFA enabled successfully',
            data: {
                mfaEnabled: true,
            }
        });
    }
    catch (error) {
        logger.error('MFA verification error:', error);
        res.status(500).json({
            success: false,
            message: 'MFA verification failed',
            code: 'MFA_VERIFICATION_ERROR'
        });
    }
});
// Verify MFA for login
router.post('/mfa/verify-login', mfaValidation, async (req, res) => {
    try {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({
                success: false,
                message: 'Validation failed',
                errors: errors.array(),
                code: 'VALIDATION_ERROR'
            });
        }
        const { token: mfaToken, accessToken } = req.body;
        if (!accessToken) {
            return res.status(400).json({
                success: false,
                message: 'Access token required',
                code: 'ACCESS_TOKEN_REQUIRED'
            });
        }
        const decoded = await TokenManager.verifyAccessToken(accessToken);
        const user = await User.findById(decoded.userId);
        if (!user || !user.mfaEnabled || !user.mfaSecret) {
            return res.status(400).json({
                success: false,
                message: 'MFA not enabled',
                code: 'MFA_NOT_ENABLED'
            });
        }
        // Verify the MFA token
        const verified = speakeasy.totp.verify({
            secret: user.mfaSecret,
            encoding: 'base32',
            token: mfaToken,
            window: 2,
        });
        if (!verified) {
            return res.status(400).json({
                success: false,
                message: 'Invalid MFA token',
                code: 'INVALID_MFA_TOKEN'
            });
        }
        // Generate new token with MFA verified
        const tokenPayload = {
            userId: user._id,
            email: user.email,
            roles: user.roles,
            jti: uuidv4(),
            mfaVerified: true,
        };
        const newAccessToken = TokenManager.generateAccessToken(tokenPayload);
        auditLogger('mfa_login_verified', 'user', user._id, {
            email: user.email,
            ip: req.ip,
        });
        res.json({
            success: true,
            message: 'MFA verification successful',
            data: {
                accessToken: newAccessToken,
                user: {
                    id: user._id,
                    email: user.email,
                    name: user.name,
                    roles: user.roles,
                    studentId: user.studentId,
                    staffId: user.staffId,
                    department: user.department,
                    level: user.level,
                    programme: user.programme,
                    mfaEnabled: user.mfaEnabled,
                }
            }
        });
    }
    catch (error) {
        logger.error('MFA login verification error:', error);
        res.status(500).json({
            success: false,
            message: 'MFA verification failed',
            code: 'MFA_VERIFICATION_ERROR'
        });
    }
});
// Disable MFA
router.post('/mfa/disable', async (req, res) => {
    try {
        const authHeader = req.headers.authorization;
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
            return res.status(401).json({
                success: false,
                message: 'Authentication required',
                code: 'AUTH_REQUIRED'
            });
        }
        const token = authHeader.substring(7);
        const decoded = await TokenManager.verifyAccessToken(token);
        const user = await User.findById(decoded.userId);
        if (!user) {
            return res.status(404).json({
                success: false,
                message: 'User not found',
                code: 'USER_NOT_FOUND'
            });
        }
        user.mfaEnabled = false;
        user.mfaSecret = undefined;
        await user.save();
        auditLogger('mfa_disabled', 'user', user._id, {
            email: user.email,
            ip: req.ip,
        });
        res.json({
            success: true,
            message: 'MFA disabled successfully',
            data: {
                mfaEnabled: false,
            }
        });
    }
    catch (error) {
        logger.error('MFA disable error:', error);
        res.status(500).json({
            success: false,
            message: 'MFA disable failed',
            code: 'MFA_DISABLE_ERROR'
        });
    }
});
export default router;
