import express from 'express';
import { body, validationResult } from 'express-validator';
import { Grade, User, Course } from '../models/index.js';
import { authenticate, requireRole } from '../middleware/auth.js';
import { logger, auditLogger } from '../utils/logger.js';
import { publishRealtimeUpdate } from '../utils/realtime.js';
const router = express.Router();
// Validation rules
const gradeValidation = [
    body('student').isMongoId().withMessage('Valid student ID required'),
    body('course').isMongoId().withMessage('Valid course ID required'),
    body('assignment').optional().isMongoId().withMessage('Valid assignment ID required'),
    body('score').isFloat({ min: 0 }).withMessage('Score must be a positive number'),
    body('maxScore').isFloat({ min: 1 }).withMessage('Max score must be at least 1'),
    body('type').isIn(['assignment', 'quiz', 'exam', 'project', 'participation']).withMessage('Invalid grade type'),
    body('comments').optional().trim(),
    body('isFinal').optional().isBoolean()
];
// GET /api/grades - List grades with filtering
router.get('/', authenticate, async (req, res) => {
    try {
        const { page = 1, limit = 20, student, course, semester, type, sortBy = 'gradedAt', sortOrder = 'desc' } = req.query;
        // Build filter query based on user role
        const filter = {};
        if (req.user.roles.includes('student')) {
            // Students can only see their own grades
            filter.student = req.user._id;
        }
        else if (req.user.roles.includes('lecturer')) {
            // Lecturers can see grades for their courses
            const lecturerCourses = await Course.find({ lecturer: req.user._id }).select('_id');
            filter.course = { $in: lecturerCourses.map(c => c._id) };
        }
        // Apply additional filters
        if (student && req.user.roles.includes('lecturer'))
            filter.student = student;
        if (course)
            filter.course = course;
        if (semester)
            filter.semester = semester;
        if (type)
            filter.type = type;
        // Calculate pagination
        const skip = (parseInt(page) - 1) * parseInt(limit);
        const sort = { [sortBy]: sortOrder === 'desc' ? -1 : 1 };
        // Execute query with pagination and population
        const grades = await Grade.find(filter)
            .populate('student', 'name email studentId')
            .populate('course', 'code title')
            .populate('assignment', 'title')
            .populate('gradedBy', 'name email')
            .sort(sort)
            .skip(skip)
            .limit(parseInt(limit));
        const total = await Grade.countDocuments(filter);
        res.json({
            success: true,
            data: {
                grades,
                pagination: {
                    page: parseInt(page),
                    limit: parseInt(limit),
                    total,
                    pages: Math.ceil(total / parseInt(limit))
                }
            }
        });
    }
    catch (error) {
        logger.error('Error fetching grades:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to fetch grades',
            code: 'FETCH_GRADES_ERROR'
        });
    }
});
// GET /api/grades/:id - Get grade by ID
router.get('/:id', authenticate, async (req, res) => {
    try {
        const { id } = req.params;
        const grade = await Grade.findById(id)
            .populate('student', 'name email studentId')
            .populate('course', 'code title')
            .populate('assignment', 'title')
            .populate('gradedBy', 'name email');
        if (!grade) {
            return res.status(404).json({
                success: false,
                message: 'Grade not found',
                code: 'GRADE_NOT_FOUND'
            });
        }
        // Check permissions
        if (req.user.roles.includes('student') && grade.student._id.toString() !== req.user._id) {
            return res.status(403).json({
                success: false,
                message: 'Access denied',
                code: 'ACCESS_DENIED'
            });
        }
        res.json({
            success: true,
            data: { grade }
        });
    }
    catch (error) {
        logger.error('Error fetching grade:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to fetch grade',
            code: 'FETCH_GRADE_ERROR'
        });
    }
});
// POST /api/grades - Create new grade (Lecturer/Admin only)
router.post('/', authenticate, requireRole('lecturer', 'admin'), gradeValidation, async (req, res) => {
    try {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({
                success: false,
                message: 'Validation failed',
                errors: errors.array(),
                code: 'VALIDATION_ERROR'
            });
        }
        const { student, course, assignment, score, maxScore, type, comments, isFinal = false } = req.body;
        // Verify student exists
        const studentUser = await User.findOne({
            _id: student,
            roles: 'student',
            isActive: true
        });
        if (!studentUser) {
            return res.status(400).json({
                success: false,
                message: 'Invalid student',
                code: 'INVALID_STUDENT'
            });
        }
        // Verify course exists and lecturer has permission
        const courseObj = await Course.findById(course);
        if (!courseObj) {
            return res.status(400).json({
                success: false,
                message: 'Invalid course',
                code: 'INVALID_COURSE'
            });
        }
        // Check if lecturer can grade this course
        if (req.user.roles.includes('lecturer') && courseObj.lecturer.toString() !== req.user._id) {
            return res.status(403).json({
                success: false,
                message: 'You can only grade your own courses',
                code: 'INSUFFICIENT_PERMISSION'
            });
        }
        // Calculate percentage
        const percentage = (score / maxScore) * 100;
        // Determine letter grade based on percentage
        let letterGrade;
        if (percentage >= 90)
            letterGrade = 'A';
        else if (percentage >= 80)
            letterGrade = 'B';
        else if (percentage >= 70)
            letterGrade = 'C';
        else if (percentage >= 60)
            letterGrade = 'D';
        else
            letterGrade = 'F';
        // Create new grade
        const grade = new Grade({
            student,
            course,
            assignment,
            score,
            maxScore,
            percentage,
            grade: letterGrade,
            type,
            semester: courseObj.semester,
            academicYear: courseObj.academicYear,
            gradedBy: req.user._id,
            gradedAt: new Date(),
            comments,
            isFinal
        });
        await grade.save();
        // Populate grade for response
        await grade.populate([
            { path: 'student', select: 'name email studentId' },
            { path: 'course', select: 'code title' },
            { path: 'assignment', select: 'title' },
            { path: 'gradedBy', select: 'name email' }
        ]);
        // Publish real-time update
        await publishRealtimeUpdate('grade_created', {
            grade: {
                id: grade._id,
                studentId: student,
                studentName: studentUser.name,
                courseId: course,
                courseCode: courseObj.code,
                score: score,
                maxScore: maxScore,
                grade: letterGrade,
                type: type
            },
            gradedBy: req.user._id
        });
        // Log creation
        auditLogger('grade_created', 'grade', req.user._id, {
            gradeId: grade._id,
            studentId: student,
            courseId: course,
            score: score,
            grade: letterGrade
        });
        res.status(201).json({
            success: true,
            message: 'Grade created successfully',
            data: { grade }
        });
    }
    catch (error) {
        logger.error('Error creating grade:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to create grade',
            code: 'CREATE_GRADE_ERROR'
        });
    }
});
// PUT /api/grades/:id - Update grade (Lecturer/Admin only)
router.put('/:id', authenticate, requireRole('lecturer', 'admin'), gradeValidation, async (req, res) => {
    try {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({
                success: false,
                message: 'Validation failed',
                errors: errors.array(),
                code: 'VALIDATION_ERROR'
            });
        }
        const { id } = req.params;
        const { score, maxScore, comments, isFinal } = req.body;
        const grade = await Grade.findById(id);
        if (!grade) {
            return res.status(404).json({
                success: false,
                message: 'Grade not found',
                code: 'GRADE_NOT_FOUND'
            });
        }
        // Check permissions
        if (req.user.roles.includes('lecturer')) {
            const course = await Course.findById(grade.course);
            if (!course || course.lecturer.toString() !== req.user._id) {
                return res.status(403).json({
                    success: false,
                    message: 'You can only modify grades for your own courses',
                    code: 'INSUFFICIENT_PERMISSION'
                });
            }
        }
        // Update grade fields
        if (score !== undefined)
            grade.score = score;
        if (maxScore !== undefined)
            grade.maxScore = maxScore;
        if (comments !== undefined)
            grade.comments = comments;
        if (isFinal !== undefined)
            grade.isFinal = isFinal;
        // Recalculate percentage and letter grade
        grade.percentage = (grade.score / grade.maxScore) * 100;
        let letterGrade;
        if (grade.percentage >= 90)
            letterGrade = 'A';
        else if (grade.percentage >= 80)
            letterGrade = 'B';
        else if (grade.percentage >= 70)
            letterGrade = 'C';
        else if (grade.percentage >= 60)
            letterGrade = 'D';
        else
            letterGrade = 'F';
        grade.grade = letterGrade;
        grade.gradedAt = new Date();
        await grade.save();
        // Populate grade for response
        await grade.populate([
            { path: 'student', select: 'name email studentId' },
            { path: 'course', select: 'code title' },
            { path: 'assignment', select: 'title' },
            { path: 'gradedBy', select: 'name email' }
        ]);
        // Publish real-time update
        await publishRealtimeUpdate('grade_updated', {
            grade: {
                id: grade._id,
                studentId: grade.student._id,
                studentName: grade.student.name,
                courseId: grade.course._id,
                courseCode: grade.course.code,
                score: grade.score,
                maxScore: grade.maxScore,
                grade: letterGrade
            },
            updatedBy: req.user._id
        });
        // Log update
        auditLogger('grade_updated', 'grade', req.user._id, {
            gradeId: grade._id,
            studentId: grade.student._id,
            changes: { score, maxScore, grade: letterGrade }
        });
        res.json({
            success: true,
            message: 'Grade updated successfully',
            data: { grade }
        });
    }
    catch (error) {
        logger.error('Error updating grade:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to update grade',
            code: 'UPDATE_GRADE_ERROR'
        });
    }
});
// DELETE /api/grades/:id - Delete grade (Lecturer/Admin only)
router.delete('/:id', authenticate, requireRole('lecturer', 'admin'), async (req, res) => {
    try {
        const { id } = req.params;
        const grade = await Grade.findById(id);
        if (!grade) {
            return res.status(404).json({
                success: false,
                message: 'Grade not found',
                code: 'GRADE_NOT_FOUND'
            });
        }
        // Check permissions
        if (req.user.roles.includes('lecturer')) {
            const course = await Course.findById(grade.course);
            if (!course || course.lecturer.toString() !== req.user._id) {
                return res.status(403).json({
                    success: false,
                    message: 'You can only delete grades for your own courses',
                    code: 'INSUFFICIENT_PERMISSION'
                });
            }
        }
        await Grade.findByIdAndDelete(id);
        // Publish real-time update
        await publishRealtimeUpdate('grade_deleted', {
            gradeId: grade._id,
            studentId: grade.student,
            courseId: grade.course,
            deletedBy: req.user._id
        });
        // Log deletion
        auditLogger('grade_deleted', 'grade', req.user._id, {
            gradeId: grade._id,
            studentId: grade.student,
            courseId: grade.course
        });
        res.json({
            success: true,
            message: 'Grade deleted successfully'
        });
    }
    catch (error) {
        logger.error('Error deleting grade:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to delete grade',
            code: 'DELETE_GRADE_ERROR'
        });
    }
});
// GET /api/grades/course/:courseId - Get grades for a specific course
router.get('/course/:courseId', authenticate, requireRole('lecturer', 'admin'), async (req, res) => {
    try {
        const { courseId } = req.params;
        const { student, type } = req.query;
        const filter = { course: courseId };
        if (student)
            filter.student = student;
        if (type)
            filter.type = type;
        // Check if lecturer can access this course
        if (req.user.roles.includes('lecturer')) {
            const course = await Course.findById(courseId);
            if (!course || course.lecturer.toString() !== req.user._id) {
                return res.status(403).json({
                    success: false,
                    message: 'Access denied to this course',
                    code: 'ACCESS_DENIED'
                });
            }
        }
        const grades = await Grade.find(filter)
            .populate('student', 'name email studentId')
            .populate('assignment', 'title')
            .sort({ student: 1, gradedAt: -1 });
        res.json({
            success: true,
            data: { grades }
        });
    }
    catch (error) {
        logger.error('Error fetching course grades:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to fetch course grades',
            code: 'FETCH_COURSE_GRADES_ERROR'
        });
    }
});
// GET /api/grades/student/:studentId - Get grades for a specific student
router.get('/student/:studentId', authenticate, async (req, res) => {
    try {
        const { studentId } = req.params;
        // Check permissions
        if (req.user.roles.includes('student') && studentId !== req.user._id) {
            return res.status(403).json({
                success: false,
                message: 'Access denied',
                code: 'ACCESS_DENIED'
            });
        }
        const grades = await Grade.find({ student: studentId })
            .populate('course', 'code title')
            .populate('assignment', 'title')
            .populate('gradedBy', 'name email')
            .sort({ gradedAt: -1 });
        res.json({
            success: true,
            data: { grades }
        });
    }
    catch (error) {
        logger.error('Error fetching student grades:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to fetch student grades',
            code: 'FETCH_STUDENT_GRADES_ERROR'
        });
    }
});
export default router;
