// Payment service implementations for Kenyan payment methods
import axios from 'axios';
import crypto from 'crypto';
import { PaymentTransaction, PaymentMethod, Fee } from '../models/Payment.js';
import { logger } from '../utils/logger.js';
import { publishRealtimeUpdate } from '../utils/realtime.js';
// M-Pesa Service Implementation
export class MpesaService {
    constructor(config) {
        this.config = config;
        this.httpClient = axios.create({
            baseURL: config.environment === 'production'
                ? 'https://api.safaricom.co.ke'
                : 'https://sandbox.safaricom.co.ke',
            timeout: 30000,
        });
    }
    async initiatePayment(request) {
        try {
            logger.info('Initiating M-Pesa payment', {
                studentId: request.studentId,
                amount: request.amount,
                phoneNumber: request.phoneNumber
            });
            // Validate phone number
            if (!this.isValidPhoneNumber(request.phoneNumber)) {
                throw new Error('Invalid phone number format');
            }
            // Get access token
            const accessToken = await this.getAccessToken();
            // Generate timestamp and password
            const timestamp = this.generateTimestamp();
            const password = this.generatePassword(timestamp);
            // Generate reference number
            const referenceNumber = this.generateReferenceNumber(request.studentId);
            // Prepare STK Push request
            const stkPushRequest = {
                BusinessShortCode: this.config.businessShortCode,
                Password: password,
                Timestamp: timestamp,
                TransactionType: 'CustomerPayBillOnline',
                Amount: Math.round(request.amount),
                PartyA: request.phoneNumber,
                PartyB: this.config.businessShortCode,
                PhoneNumber: request.phoneNumber,
                CallBackURL: this.config.callbackUrl,
                AccountReference: referenceNumber,
                TransactionDesc: request.description || 'University fee payment',
            };
            logger.debug('M-Pesa STK Push request', { stkPushRequest });
            // Make API call
            const response = await this.httpClient.post('/mpesa/stkpush/v1/processrequest', stkPushRequest, {
                headers: {
                    'Authorization': `Bearer ${accessToken}`,
                    'Content-Type': 'application/json',
                },
            });
            logger.info('M-Pesa payment initiated successfully', {
                checkoutRequestId: response.data.CheckoutRequestID,
                merchantRequestId: response.data.MerchantRequestID
            });
            return {
                success: true,
                transactionId: response.data.CheckoutRequestID,
                referenceNumber: referenceNumber,
                message: 'Payment initiated successfully. Please complete the payment on your phone.',
                providerResponse: response.data
            };
        }
        catch (error) {
            logger.error('M-Pesa payment initiation failed', { error: error.message });
            return {
                success: false,
                message: 'Payment initiation failed',
                error: error.message
            };
        }
    }
    async verifyPayment(checkoutRequestId) {
        try {
            const accessToken = await this.getAccessToken();
            const response = await this.httpClient.post('/mpesa/stkpushquery/v1/query', {
                BusinessShortCode: this.config.businessShortCode,
                Password: this.generatePassword(this.generateTimestamp()),
                Timestamp: this.generateTimestamp(),
                CheckoutRequestID: checkoutRequestId
            }, {
                headers: {
                    'Authorization': `Bearer ${accessToken}`,
                    'Content-Type': 'application/json',
                },
            });
            return response.data;
        }
        catch (error) {
            logger.error('M-Pesa payment verification failed', { error: error.message });
            throw error;
        }
    }
    async getAccessToken() {
        try {
            const auth = Buffer.from(`${this.config.consumerKey}:${this.config.consumerSecret}`).toString('base64');
            const response = await this.httpClient.get('/oauth/v1/generate?grant_type=client_credentials', {
                headers: {
                    'Authorization': `Basic ${auth}`,
                },
            });
            return response.data.access_token;
        }
        catch (error) {
            logger.error('Failed to get M-Pesa access token', { error: error.message });
            throw error;
        }
    }
    generateTimestamp() {
        return new Date().toISOString().replace(/[^0-9]/g, '').slice(0, -3);
    }
    generatePassword(timestamp) {
        const data = `${this.config.businessShortCode}${this.config.passkey}${timestamp}`;
        return Buffer.from(data).toString('base64');
    }
    generateReferenceNumber(studentId) {
        const timestamp = Date.now().toString().slice(-6);
        return `UNI${studentId.slice(-4)}${timestamp}`;
    }
    isValidPhoneNumber(phoneNumber) {
        // Remove any non-digit characters
        const cleaned = phoneNumber.replace(/\D/g, '');
        // Check if it's a valid Kenyan phone number
        return /^254[17]\d{8}$/.test(cleaned);
    }
}
// JamboPay Service Implementation
export class JamboPayService {
    constructor(config) {
        this.config = config;
        this.httpClient = axios.create({
            baseURL: config.environment === 'production'
                ? 'https://api.jambopay.com'
                : 'https://sandbox.jambopay.com',
            timeout: 30000,
        });
    }
    async initiatePayment(request) {
        try {
            logger.info('Initiating JamboPay payment', {
                studentId: request.studentId,
                amount: request.amount
            });
            const referenceNumber = this.generateReferenceNumber(request.studentId);
            const paymentRequest = {
                merchantId: this.config.merchantId,
                amount: request.amount,
                currency: 'KES',
                reference: referenceNumber,
                description: request.description || 'University fee payment',
                callbackUrl: this.config.callbackUrl,
                phoneNumber: request.phoneNumber,
                metadata: {
                    studentId: request.studentId,
                    feeId: request.feeId
                }
            };
            const response = await this.httpClient.post('/api/v1/payments', paymentRequest, {
                headers: {
                    'Authorization': `Bearer ${this.config.apiKey}`,
                    'Content-Type': 'application/json',
                },
            });
            logger.info('JamboPay payment initiated successfully', {
                transactionId: response.data.transactionId
            });
            return {
                success: true,
                transactionId: response.data.transactionId,
                referenceNumber: referenceNumber,
                message: 'Payment initiated successfully',
                providerResponse: response.data
            };
        }
        catch (error) {
            logger.error('JamboPay payment initiation failed', { error: error.message });
            return {
                success: false,
                message: 'Payment initiation failed',
                error: error.message
            };
        }
    }
    generateReferenceNumber(studentId) {
        const timestamp = Date.now().toString().slice(-6);
        return `JP${studentId.slice(-4)}${timestamp}`;
    }
}
// I&M Bank Service Implementation
export class ImBankService {
    constructor(config) {
        this.config = config;
        this.httpClient = axios.create({
            baseURL: config.baseUrl,
            timeout: 30000,
        });
    }
    async initiatePayment(request) {
        try {
            logger.info('Initiating I&M Bank payment', {
                studentId: request.studentId,
                amount: request.amount
            });
            const accessToken = await this.getAccessToken();
            const referenceNumber = this.generateReferenceNumber(request.studentId);
            const transferRequest = {
                accountNumber: request.accountNumber,
                amount: request.amount,
                referenceNumber: referenceNumber,
                description: request.description || 'University fee payment',
                currency: 'KES',
                timestamp: new Date().toISOString(),
                metadata: {
                    studentId: request.studentId,
                    feeId: request.feeId
                }
            };
            const response = await this.httpClient.post('/api/v1/transfers', transferRequest, {
                headers: {
                    'Authorization': `Bearer ${accessToken}`,
                    'Content-Type': 'application/json',
                },
            });
            logger.info('I&M Bank payment initiated successfully', {
                transactionId: response.data.transactionId
            });
            return {
                success: true,
                transactionId: response.data.transactionId,
                referenceNumber: referenceNumber,
                message: 'Bank transfer initiated successfully',
                providerResponse: response.data
            };
        }
        catch (error) {
            logger.error('I&M Bank payment initiation failed', { error: error.message });
            return {
                success: false,
                message: 'Bank transfer initiation failed',
                error: error.message
            };
        }
    }
    async getAccessToken() {
        try {
            const response = await this.httpClient.post('/oauth/token', {
                grant_type: 'client_credentials',
                client_id: this.config.clientId,
                client_secret: this.config.clientSecret,
            });
            return response.data.access_token;
        }
        catch (error) {
            logger.error('Failed to get I&M Bank access token', { error: error.message });
            throw error;
        }
    }
    generateReferenceNumber(studentId) {
        const timestamp = Date.now().toString().slice(-6);
        return `IM${studentId.slice(-4)}${timestamp}`;
    }
}
// Payment Service Manager
export class PaymentServiceManager {
    constructor(config) {
        this.config = config;
        this.mpesaService = new MpesaService(config.mpesa);
        this.jambopayService = new JamboPayService(config.jambopay);
        this.imBankService = new ImBankService(config.imBank);
    }
    async initiatePayment(paymentMethod, request) {
        try {
            // Create payment transaction record
            const paymentMethodDoc = await PaymentMethod.findOne({ code: paymentMethod });
            if (!paymentMethodDoc) {
                throw new Error('Invalid payment method');
            }
            const transaction = new PaymentTransaction({
                student: request.studentId,
                fee: request.feeId,
                amount: request.amount,
                paymentMethod: paymentMethodDoc._id,
                referenceNumber: this.generateReferenceNumber(request.studentId),
                metadata: {
                    phoneNumber: request.phoneNumber,
                    accountNumber: request.accountNumber,
                    bankCode: request.bankCode
                },
                netAmount: request.amount
            });
            await transaction.save();
            // Initiate payment based on method
            let paymentResponse;
            switch (paymentMethod) {
                case 'mpesa':
                    paymentResponse = await this.mpesaService.initiatePayment(request);
                    break;
                case 'jambopay':
                    paymentResponse = await this.jambopayService.initiatePayment(request);
                    break;
                case 'im_bank':
                    paymentResponse = await this.imBankService.initiatePayment(request);
                    break;
                default:
                    throw new Error(`Unsupported payment method: ${paymentMethod}`);
            }
            // Update transaction with provider response
            if (paymentResponse.success) {
                transaction.providerTransactionId = paymentResponse.transactionId;
                transaction.status = 'initiated';
                transaction.referenceNumber = paymentResponse.referenceNumber;
                transaction.metadata.providerResponse = paymentResponse.providerResponse;
            }
            else {
                transaction.status = 'failed';
                transaction.failureReason = paymentResponse.error;
                transaction.failedAt = new Date();
            }
            await transaction.save();
            // Publish real-time update
            await publishRealtimeUpdate('payment_initiated', {
                transaction: {
                    id: transaction._id,
                    studentId: request.studentId,
                    amount: request.amount,
                    paymentMethod: paymentMethod,
                    status: transaction.status
                },
                initiatedBy: request.studentId
            });
            return {
                ...paymentResponse,
                referenceNumber: transaction.referenceNumber
            };
        }
        catch (error) {
            logger.error('Payment initiation failed', { error: error.message });
            return {
                success: false,
                message: 'Payment initiation failed',
                error: error.message
            };
        }
    }
    async handleWebhook(provider, payload, signature) {
        try {
            logger.info('Processing webhook', { provider, eventType: payload.eventType });
            // Verify webhook signature
            if (!this.verifyWebhookSignature(provider, payload, signature)) {
                throw new Error('Invalid webhook signature');
            }
            // Create webhook record
            const webhook = new PaymentWebhook({
                provider,
                eventType: payload.eventType,
                payload,
                signature
            });
            await webhook.save();
            // Process webhook based on provider and event type
            await this.processWebhook(webhook);
        }
        catch (error) {
            logger.error('Webhook processing failed', { error: error.message });
            throw error;
        }
    }
    async processWebhook(webhook) {
        try {
            const { payload } = webhook;
            // Find transaction by provider transaction ID
            const transaction = await PaymentTransaction.findOne({
                providerTransactionId: payload.transactionId || payload.CheckoutRequestID
            });
            if (!transaction) {
                logger.warn('Transaction not found for webhook', {
                    providerTransactionId: payload.transactionId || payload.CheckoutRequestID
                });
                return;
            }
            // Process based on provider
            switch (webhook.provider) {
                case 'mpesa':
                    await this.processMpesaWebhook(transaction, payload);
                    break;
                case 'jambopay':
                    await this.processJamboPayWebhook(transaction, payload);
                    break;
                case 'im_bank':
                    await this.processImBankWebhook(transaction, payload);
                    break;
            }
            // Mark webhook as processed
            webhook.processed = true;
            webhook.processedAt = new Date();
            await webhook.save();
        }
        catch (error) {
            logger.error('Webhook processing failed', { error: error.message });
            // Increment processing attempts
            webhook.processingAttempts += 1;
            await webhook.save();
            throw error;
        }
    }
    async processMpesaWebhook(transaction, payload) {
        const { Body } = payload;
        const { stkCallback } = Body;
        if (stkCallback.ResultCode === 0) {
            // Payment successful
            transaction.status = 'completed';
            transaction.completedAt = new Date();
            transaction.metadata.callbackData = stkCallback;
            // Update fee payment status
            const fee = await Fee.findById(transaction.fee);
            if (fee) {
                fee.isPaid = true;
                fee.paidAt = new Date();
                fee.paymentTransaction = transaction._id;
                await fee.save();
            }
            // Send success notification
            await publishRealtimeUpdate('payment_completed', {
                transaction: {
                    id: transaction._id,
                    studentId: transaction.student,
                    amount: transaction.amount,
                    status: 'completed'
                },
                completedBy: transaction.student
            });
        }
        else {
            // Payment failed
            transaction.status = 'failed';
            transaction.failedAt = new Date();
            transaction.failureReason = stkCallback.ResultDesc;
            transaction.metadata.callbackData = stkCallback;
            // Send failure notification
            await publishRealtimeUpdate('payment_failed', {
                transaction: {
                    id: transaction._id,
                    studentId: transaction.student,
                    amount: transaction.amount,
                    status: 'failed',
                    reason: stkCallback.ResultDesc
                },
                failedBy: transaction.student
            });
        }
        await transaction.save();
    }
    async processJamboPayWebhook(transaction, payload) {
        if (payload.status === 'completed') {
            transaction.status = 'completed';
            transaction.completedAt = new Date();
            transaction.metadata.callbackData = payload;
            // Update fee payment status
            const fee = await Fee.findById(transaction.fee);
            if (fee) {
                fee.isPaid = true;
                fee.paidAt = new Date();
                fee.paymentTransaction = transaction._id;
                await fee.save();
            }
        }
        else if (payload.status === 'failed') {
            transaction.status = 'failed';
            transaction.failedAt = new Date();
            transaction.failureReason = payload.reason;
            transaction.metadata.callbackData = payload;
        }
        await transaction.save();
    }
    async processImBankWebhook(transaction, payload) {
        if (payload.status === 'success') {
            transaction.status = 'completed';
            transaction.completedAt = new Date();
            transaction.metadata.callbackData = payload;
            // Update fee payment status
            const fee = await Fee.findById(transaction.fee);
            if (fee) {
                fee.isPaid = true;
                fee.paidAt = new Date();
                fee.paymentTransaction = transaction._id;
                await fee.save();
            }
        }
        else if (payload.status === 'failed') {
            transaction.status = 'failed';
            transaction.failedAt = new Date();
            transaction.failureReason = payload.error;
            transaction.metadata.callbackData = payload;
        }
        await transaction.save();
    }
    verifyWebhookSignature(provider, payload, signature) {
        // Implement signature verification based on provider
        switch (provider) {
            case 'mpesa':
                return this.verifyMpesaSignature(payload, signature);
            case 'jambopay':
                return this.verifyJamboPaySignature(payload, signature);
            case 'im_bank':
                return this.verifyImBankSignature(payload, signature);
            default:
                return false;
        }
    }
    verifyMpesaSignature(payload, signature) {
        const expectedSignature = crypto
            .createHmac('sha256', process.env.MPESA_WEBHOOK_SECRET)
            .update(JSON.stringify(payload))
            .digest('hex');
        return signature === expectedSignature;
    }
    verifyJamboPaySignature(payload, signature) {
        const expectedSignature = crypto
            .createHmac('sha256', this.config.jambopay.apiKey)
            .update(JSON.stringify(payload))
            .digest('hex');
        return signature === expectedSignature;
    }
    verifyImBankSignature(payload, signature) {
        const expectedSignature = crypto
            .createHmac('sha256', this.config.imBank.clientSecret)
            .update(JSON.stringify(payload))
            .digest('hex');
        return signature === expectedSignature;
    }
    generateReferenceNumber(studentId) {
        const timestamp = Date.now().toString().slice(-6);
        return `UNI${studentId.slice(-4)}${timestamp}`;
    }
}
export default PaymentServiceManager;
