import winston from 'winston';
import config from '../config/index.js';
// Custom format for development
const devFormat = winston.format.combine(winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }), winston.format.errors({ stack: true }), winston.format.colorize(), winston.format.printf(({ timestamp, level, message, stack, ...meta }) => {
    let log = `${timestamp} [${level}]: ${message}`;
    if (Object.keys(meta).length > 0) {
        log += ` ${JSON.stringify(meta)}`;
    }
    if (stack) {
        log += `\n${stack}`;
    }
    return log;
}));
// Custom format for production
const prodFormat = winston.format.combine(winston.format.timestamp(), winston.format.errors({ stack: true }), winston.format.json());
// Create logger instance
const logger = winston.createLogger({
    level: config.monitoring.logging.level,
    format: config.monitoring.logging.format === 'json' ? prodFormat : devFormat,
    defaultMeta: {
        service: 'university-portal-api',
        version: process.env.npm_package_version || '1.0.0',
    },
    transports: [
        // Console transport
        new winston.transports.Console({
            handleExceptions: true,
            handleRejections: true,
        }),
        // File transports for production
        ...(config.server.env === 'production' ? [
            new winston.transports.File({
                filename: 'logs/error.log',
                level: 'error',
                maxsize: 5242880, // 5MB
                maxFiles: 5,
                handleExceptions: true,
                handleRejections: true,
            }),
            new winston.transports.File({
                filename: 'logs/combined.log',
                maxsize: 5242880, // 5MB
                maxFiles: 5,
            }),
        ] : []),
    ],
    // Handle uncaught exceptions and unhandled rejections
    exceptionHandlers: [
        new winston.transports.Console({
            format: winston.format.simple(),
        }),
    ],
    rejectionHandlers: [
        new winston.transports.Console({
            format: winston.format.simple(),
        }),
    ],
});
// Add request logging middleware
export const requestLogger = (req, res, next) => {
    const start = Date.now();
    // Log request
    logger.info('Request started', {
        method: req.method,
        url: req.url,
        ip: req.ip,
        userAgent: req.get('User-Agent'),
        userId: req.user?._id,
    });
    // Override res.end to log response
    const originalEnd = res.end;
    res.end = function (chunk, encoding) {
        const duration = Date.now() - start;
        logger.info('Request completed', {
            method: req.method,
            url: req.url,
            statusCode: res.statusCode,
            duration: `${duration}ms`,
            ip: req.ip,
            userId: req.user?._id,
        });
        originalEnd.call(this, chunk, encoding);
    };
    next();
};
// Add error logging middleware
export const errorLogger = (error, req, res, next) => {
    logger.error('Request error', {
        error: error.message,
        stack: error.stack,
        method: req.method,
        url: req.url,
        ip: req.ip,
        userId: req.user?._id,
        body: req.body,
        query: req.query,
        params: req.params,
    });
    next(error);
};
// Performance logging
export const performanceLogger = (operation, duration, metadata = {}) => {
    logger.info('Performance metric', {
        operation,
        duration: `${duration}ms`,
        ...metadata,
    });
};
// Security logging
export const securityLogger = (event, details = {}) => {
    logger.warn('Security event', {
        event,
        timestamp: new Date().toISOString(),
        ...details,
    });
};
// Audit logging
export const auditLogger = (action, resource, userId, details = {}) => {
    logger.info('Audit log', {
        action,
        resource,
        userId,
        timestamp: new Date().toISOString(),
        ...details,
    });
};
// Database operation logging
export const dbLogger = (operation, collection, duration, metadata = {}) => {
    logger.debug('Database operation', {
        operation,
        collection,
        duration: `${duration}ms`,
        ...metadata,
    });
};
// Cache operation logging
export const cacheLogger = (operation, key, hit, duration, metadata = {}) => {
    logger.debug('Cache operation', {
        operation,
        key,
        hit,
        duration: `${duration}ms`,
        ...metadata,
    });
};
// Rate limiting logging
export const rateLimitLogger = (ip, endpoint, limit, remaining) => {
    logger.warn('Rate limit warning', {
        ip,
        endpoint,
        limit,
        remaining,
        timestamp: new Date().toISOString(),
    });
};
// Health check logging
export const healthLogger = (service, status, details = {}) => {
    const level = status === 'healthy' ? 'info' : 'error';
    logger[level]('Health check', {
        service,
        status,
        timestamp: new Date().toISOString(),
        ...details,
    });
};
// Custom log levels for different contexts
export const logLevels = {
    ERROR: 'error',
    WARN: 'warn',
    INFO: 'info',
    DEBUG: 'debug',
};
// Structured logging helpers
export const logStructured = (level, message, metadata = {}) => {
    logger.log(level, message, metadata);
};
export const logError = (message, error, metadata = {}) => {
    logger.error(message, {
        error: error.message,
        stack: error.stack,
        ...metadata,
    });
};
export const logWarning = (message, metadata = {}) => {
    logger.warn(message, metadata);
};
export const logInfo = (message, metadata = {}) => {
    logger.info(message, metadata);
};
export const logDebug = (message, metadata = {}) => {
    logger.debug(message, metadata);
};
// Export the main logger instance
export { logger };
export default logger;
