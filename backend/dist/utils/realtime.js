import { redisService } from '../services/redis.js';
import { logger } from './logger.js';
// Real-time update publisher
export async function publishRealtimeUpdate(eventType, data) {
    try {
        const message = {
            type: eventType,
            data,
            timestamp: new Date().toISOString(),
            id: generateMessageId()
        };
        // Publish to Redis for Socket.io to pick up
        await redisService.publish('realtime_updates', message);
        logger.debug('Real-time update published:', { eventType, data });
    }
    catch (error) {
        logger.error('Error publishing real-time update:', error);
    }
}
// Generate unique message ID
function generateMessageId() {
    return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}
// Real-time event types
export const REALTIME_EVENTS = {
    // User events
    USER_CREATED: 'user_created',
    USER_UPDATED: 'user_updated',
    USER_DELETED: 'user_deleted',
    PROFILE_UPDATED: 'profile_updated',
    // Course events
    COURSE_CREATED: 'course_created',
    COURSE_UPDATED: 'course_updated',
    COURSE_DELETED: 'course_deleted',
    COURSE_ENROLLED: 'course_enrolled',
    COURSE_DROPPED: 'course_dropped',
    // Grade events
    GRADE_CREATED: 'grade_created',
    GRADE_UPDATED: 'grade_updated',
    GRADE_DELETED: 'grade_deleted',
    // Assignment events
    ASSIGNMENT_CREATED: 'assignment_created',
    ASSIGNMENT_UPDATED: 'assignment_updated',
    ASSIGNMENT_DELETED: 'assignment_deleted',
    ASSIGNMENT_SUBMITTED: 'assignment_submitted',
    ASSIGNMENT_GRADED: 'assignment_graded',
    // Attendance events
    ATTENDANCE_MARKED: 'attendance_marked',
    ATTENDANCE_UPDATED: 'attendance_updated',
    // Session events
    SESSION_CREATED: 'session_created',
    SESSION_UPDATED: 'session_updated',
    SESSION_STARTED: 'session_started',
    SESSION_ENDED: 'session_ended',
    // Notification events
    NOTIFICATION_CREATED: 'notification_created',
    NOTIFICATION_READ: 'notification_read',
    // System events
    SYSTEM_MAINTENANCE: 'system_maintenance',
    SYSTEM_ALERT: 'system_alert'
};
// Role-based namespace mapping
export const ROLE_NAMESPACES = {
    student: '/student',
    lecturer: '/lecturer',
    admin: '/admin',
    finance: '/finance',
    registrar: '/registrar'
};
// Get namespace for user roles
export function getNamespaceForRoles(roles) {
    // Return the first matching namespace based on role priority
    const rolePriority = ['admin', 'finance', 'registrar', 'lecturer', 'student'];
    for (const role of rolePriority) {
        if (roles.includes(role)) {
            return ROLE_NAMESPACES[role];
        }
    }
    return '/default';
}
// Filter events based on user roles and permissions
export function filterEventForUser(eventType, data, userRoles) {
    // Define which events each role can receive
    const roleEventPermissions = {
        student: [
            REALTIME_EVENTS.COURSE_CREATED,
            REALTIME_EVENTS.COURSE_UPDATED,
            REALTIME_EVENTS.GRADE_CREATED,
            REALTIME_EVENTS.GRADE_UPDATED,
            REALTIME_EVENTS.ASSIGNMENT_CREATED,
            REALTIME_EVENTS.ASSIGNMENT_UPDATED,
            REALTIME_EVENTS.ASSIGNMENT_GRADED,
            REALTIME_EVENTS.ATTENDANCE_MARKED,
            REALTIME_EVENTS.SESSION_CREATED,
            REALTIME_EVENTS.SESSION_STARTED,
            REALTIME_EVENTS.NOTIFICATION_CREATED,
            REALTIME_EVENTS.SYSTEM_ALERT
        ],
        lecturer: [
            REALTIME_EVENTS.USER_CREATED,
            REALTIME_EVENTS.COURSE_CREATED,
            REALTIME_EVENTS.COURSE_UPDATED,
            REALTIME_EVENTS.COURSE_ENROLLED,
            REALTIME_EVENTS.COURSE_DROPPED,
            REALTIME_EVENTS.ASSIGNMENT_SUBMITTED,
            REALTIME_EVENTS.ATTENDANCE_MARKED,
            REALTIME_EVENTS.SESSION_CREATED,
            REALTIME_EVENTS.SESSION_UPDATED,
            REALTIME_EVENTS.NOTIFICATION_CREATED,
            REALTIME_EVENTS.SYSTEM_ALERT
        ],
        admin: [
            // Admins can receive all events
            ...Object.values(REALTIME_EVENTS)
        ],
        finance: [
            REALTIME_EVENTS.USER_CREATED,
            REALTIME_EVENTS.COURSE_CREATED,
            REALTIME_EVENTS.COURSE_UPDATED,
            REALTIME_EVENTS.NOTIFICATION_CREATED,
            REALTIME_EVENTS.SYSTEM_ALERT
        ],
        registrar: [
            REALTIME_EVENTS.USER_CREATED,
            REALTIME_EVENTS.USER_UPDATED,
            REALTIME_EVENTS.COURSE_CREATED,
            REALTIME_EVENTS.COURSE_UPDATED,
            REALTIME_EVENTS.COURSE_ENROLLED,
            REALTIME_EVENTS.COURSE_DROPPED,
            REALTIME_EVENTS.NOTIFICATION_CREATED,
            REALTIME_EVENTS.SYSTEM_ALERT
        ]
    };
    // Check if user has permission to receive this event
    for (const role of userRoles) {
        if (roleEventPermissions[role] && roleEventPermissions[role].includes(eventType)) {
            return true;
        }
    }
    return false;
}
// Create filtered message for specific user
export function createFilteredMessage(eventType, data, userRoles, userId) {
    if (!filterEventForUser(eventType, data, userRoles)) {
        return null;
    }
    return {
        type: eventType,
        data: filterDataForUser(data, userRoles, userId),
        timestamp: new Date().toISOString(),
        id: generateMessageId()
    };
}
// Filter data based on user permissions
function filterDataForUser(data, userRoles, userId) {
    // Create a copy of data to avoid modifying original
    const filteredData = { ...data };
    // Remove sensitive information based on role
    if (!userRoles.includes('admin')) {
        // Remove sensitive fields for non-admin users
        delete filteredData.internalNotes;
        delete filteredData.systemFlags;
    }
    // Students can only see their own data
    if (userRoles.includes('student') && !userRoles.includes('lecturer') && !userRoles.includes('admin')) {
        if (filteredData.studentId && filteredData.studentId !== userId) {
            return null; // Don't send data about other students
        }
    }
    return filteredData;
}
// Rate limiting for real-time updates
const updateCounts = new Map();
export function checkUpdateRateLimit(userId, eventType) {
    const key = `${userId}_${eventType}`;
    const now = Date.now();
    const windowMs = 60000; // 1 minute window
    const maxUpdates = 10; // Max 10 updates per minute per event type
    if (!updateCounts.has(key)) {
        updateCounts.set(key, { count: 1, windowStart: now });
        return true;
    }
    const userCounts = updateCounts.get(key);
    // Reset window if expired
    if (now - userCounts.windowStart > windowMs) {
        userCounts.count = 1;
        userCounts.windowStart = now;
        return true;
    }
    // Check if limit exceeded
    if (userCounts.count >= maxUpdates) {
        return false;
    }
    userCounts.count++;
    return true;
}
// Clean up old rate limit entries
setInterval(() => {
    const now = Date.now();
    const windowMs = 60000;
    for (const [key, counts] of updateCounts.entries()) {
        if (now - counts.windowStart > windowMs) {
            updateCounts.delete(key);
        }
    }
}, 300000); // Clean up every 5 minutes
