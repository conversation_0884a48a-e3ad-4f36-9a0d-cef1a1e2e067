import dotenv from 'dotenv';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Load environment variables
dotenv.config({ path: path.join(__dirname, '../../.env') });

const config = {
  // Server Configuration
  server: {
    port: parseInt(process.env.PORT, 10) || 3001,
    host: process.env.HOST || '0.0.0.0',
    env: process.env.NODE_ENV || 'development',
    trustProxy: process.env.TRUST_PROXY === 'true',
  },

  // Database Configuration
  database: {
    uri: process.env.MONGODB_URI || 'mongodb://localhost:27017/university_portal',
    options: {
      maxPoolSize: 100,
      serverSelectionTimeoutMS: 5000,
      socketTimeoutMS: 45000,
      bufferMaxEntries: 0,
      bufferCommands: false,
      ...JSON.parse(process.env.MONGODB_OPTIONS || '{}'),
    },
  },

  // Redis Configuration
  redis: {
    url: process.env.REDIS_URL || 'redis://localhost:6379',
    cluster: {
      nodes: process.env.REDIS_CLUSTER_NODES?.split(',') || [
        'redis://localhost:7000',
        'redis://localhost:7001',
        'redis://localhost:7002',
        'redis://localhost:7003',
        'redis://localhost:7004',
        'redis://localhost:7005',
      ],
      options: {
        password: process.env.REDIS_PASSWORD || undefined,
        retryDelayOnFailover: 100,
        enableReadyCheck: false,
        maxRetriesPerRequest: null,
      },
    },
  },

  // JWT Configuration
  jwt: {
    secret: process.env.JWT_SECRET || 'your-super-secret-jwt-key',
    refreshSecret: process.env.JWT_REFRESH_SECRET || 'your-super-secret-refresh-key',
    accessExpiresIn: process.env.JWT_ACCESS_EXPIRES_IN || '15m',
    refreshExpiresIn: process.env.JWT_REFRESH_EXPIRES_IN || '7d',
    issuer: process.env.JWT_ISSUER || 'university-portal-api',
    audience: process.env.JWT_AUDIENCE || 'university-portal-client',
    algorithm: 'RS256',
  },

  // Security Configuration
  security: {
    bcryptRounds: parseInt(process.env.BCRYPT_ROUNDS, 10) || 12,
    argon2: {
      memoryLimit: parseInt(process.env.ARGON2_MEMORY_LIMIT, 10) || 65536,
      timeCost: parseInt(process.env.ARGON2_TIME_COST, 10) || 3,
      parallelism: parseInt(process.env.ARGON2_PARALLELISM, 10) || 1,
    },
  },

  // Rate Limiting Configuration
  rateLimit: {
    windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS, 10) || 300000, // 5 minutes
    maxRequests: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS, 10) || 120,
    skipSuccessfulRequests: process.env.RATE_LIMIT_SKIP_SUCCESSFUL_REQUESTS === 'true',
  },

  // File Upload Configuration
  upload: {
    maxFileSize: parseInt(process.env.MAX_FILE_SIZE, 10) || 5242880, // 5MB
    allowedTypes: process.env.ALLOWED_FILE_TYPES?.split(',') || [
      'application/pdf',
      'image/jpeg',
      'image/png',
      'image/gif',
    ],
    uploadPath: process.env.UPLOAD_PATH || './uploads',
    s3: {
      bucket: process.env.S3_BUCKET || 'university-portal-files',
      region: process.env.S3_REGION || 'us-east-1',
      accessKeyId: process.env.S3_ACCESS_KEY_ID,
      secretAccessKey: process.env.S3_SECRET_ACCESS_KEY,
    },
  },

  // Email Configuration
  email: {
    smtp: {
      host: process.env.SMTP_HOST || 'smtp.gmail.com',
      port: parseInt(process.env.SMTP_PORT, 10) || 587,
      secure: process.env.SMTP_SECURE === 'true',
      auth: {
        user: process.env.SMTP_USER,
        pass: process.env.SMTP_PASS,
      },
    },
    from: {
      email: process.env.FROM_EMAIL || '<EMAIL>',
      name: process.env.FROM_NAME || 'University Portal',
    },
  },

  // MFA Configuration
  mfa: {
    issuer: process.env.MFA_ISSUER || 'University Portal',
    algorithm: process.env.MFA_ALGORITHM || 'sha1',
    digits: parseInt(process.env.MFA_DIGITS, 10) || 6,
    period: parseInt(process.env.MFA_PERIOD, 10) || 30,
  },

  // Monitoring & Observability
  monitoring: {
    prometheus: {
      port: parseInt(process.env.PROMETHEUS_PORT, 10) || 9090,
    },
    jaeger: {
      endpoint: process.env.JAEGER_ENDPOINT || 'http://localhost:14268/api/traces',
    },
    logging: {
      level: process.env.LOG_LEVEL || 'info',
      format: process.env.LOG_FORMAT || 'json',
    },
  },

  // Socket.io Configuration
  socket: {
    rateLimit: {
      max: parseInt(process.env.SOCKET_RATE_LIMIT_MAX, 10) || 10,
      window: parseInt(process.env.SOCKET_RATE_LIMIT_WINDOW, 10) || 1000,
    },
  },

  // Cache Configuration
  cache: {
    ttl: {
      courses: parseInt(process.env.CACHE_TTL_COURSES, 10) || 60,
      grades: parseInt(process.env.CACHE_TTL_GRADES, 10) || 60,
      users: parseInt(process.env.CACHE_TTL_USERS, 10) || 300,
      permissions: parseInt(process.env.CACHE_TTL_PERMISSIONS, 10) || 1800,
    },
  },

  // Kubernetes Configuration
  kubernetes: {
    namespace: process.env.KUBERNETES_NAMESPACE || 'university-portal',
    serviceName: process.env.KUBERNETES_SERVICE_NAME || 'university-portal-api',
    replicas: parseInt(process.env.KUBERNETES_REPLICAS, 10) || 20,
    maxReplicas: parseInt(process.env.KUBERNETES_MAX_REPLICAS, 10) || 60,
    cpuTarget: parseInt(process.env.KUBERNETES_CPU_TARGET, 10) || 60,
    resources: {
      memory: process.env.KUBERNETES_MEMORY_LIMIT || '512Mi',
      cpu: process.env.KUBERNETES_CPU_LIMIT || '500m',
    },
  },

  // Health Check Configuration
  healthCheck: {
    interval: parseInt(process.env.HEALTH_CHECK_INTERVAL, 10) || 30000,
    timeout: parseInt(process.env.HEALTH_CHECK_TIMEOUT, 10) || 5000,
  },

  // Feature Flags
  features: {
    mfa: process.env.ENABLE_MFA === 'true',
    fileUpload: process.env.ENABLE_FILE_UPLOAD === 'true',
    realTime: process.env.ENABLE_REAL_TIME === 'true',
    caching: process.env.ENABLE_CACHING === 'true',
    metrics: process.env.ENABLE_METRICS === 'true',
    tracing: process.env.ENABLE_TRACING === 'true',
  },

  // Development Configuration
  development: {
    seedDatabase: process.env.SEED_DATABASE === 'true',
    hotReload: process.env.HOT_RELOAD === 'true',
    debugMode: process.env.DEBUG_MODE === 'true',
  },
};

export default config;
